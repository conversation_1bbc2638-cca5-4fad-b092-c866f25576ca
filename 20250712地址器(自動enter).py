import tkinter as tk
from tkinter import font
import pyautogui
import pyperclip
import threading
import time
import pygetwindow as gw
import winsound  # 新增 winsound 模組

TARGET_WINDOW_TITLE = "BlueStacks"  # 改成你要貼上的視窗標題

addresses = [
    "彰化縣彰化市光復路62(光復路-順安醫院)",
    "彰化縣彰化市中山路二段425號(東民街61巷)",
    "彰化縣政府(東民街下)",
    "中區國稅局彰化(國稅局-光華街)",
    "彰化縣彰化戶政事務所(中興路100巷-彰化戶政)",
    "彰化縣彰化市中興路105(戶政下-中興路+中興路)",
    "彰化縣彰化市中興路75號(中興路+建興路)",
    "彰化縣彰化市南郭路一段326(南郭路)",
    "彰化縣彰化市埔西街132(埔西街)",
    "彰化南興國小",
    "彰化信義國小",
    "彰化白沙國小(菊中街)",
    "彰化馬興國小",
    "彰化大竹國小(彰南路)",
    "彰化縣彰化市向陽街158(向陽街)",
    "彰化縣彰化市中山路一段213號(興中街)",
    "彰化縣彰化市卦山路46巷(龍泉溪)",
    "彰化縣彰化市公園路二段3巷(公園路3巷)",
    "彰化縣彰化市工校街20號(彰工)",
    "彰化縣彰化市進德路11-1(進德路-師範大學)",
    "彰化縣立陽明國民中學(長順街)",
    "彰化縣彰化市中山路二段500號(彰化縣文學館)",
    "彰化縣彰化市卦山路4號(彰化市立圖書館 city)",
    "彰化縣立圖書館(city上)",
    "彰化縣彰化市卦山路3號(卦山路+卦山路)",
    "彰化縣彰化市卦山路13號(藝術高中圖書館)",
    "彰化縣彰化市縱貫公路678(永安、永昌)",
    "彰化縣彰化市縱貫公路621-559(中山路三段-國聖)",
    "三竹路40巷",
    "彰化縣彰化市寶山路126巷2弄2號(寶山路)",
    "彰化縣和美鎮138縣道228-274號(水源路-福安廟)",
    "彰化縣和美鎮美寮路一段390號(美寮路)12",
    "彰化縣和美鎮彰新路二段500號(彰新路二段-十茂路)",
    "彰化縣彰化市中正路二段467號(中正路二段461巷)",
    "彰化縣立彰安國民中學(中正路二段525巷)",
    "3H83+GPF 介壽里 彰化縣彰化市(彰化縣立體育場)",
    "彰化縣彰化市彰馬路45號(彰馬路61巷)",
    "彰化縣秀水鄉彰水路一段191巷6(彰水路一段191巷)",
    "彰化縣彰化市134縣道387號(民生路402巷、375巷)",
    "彰化縣彰化市自強路376號(自強路)",
    "彰化縣彰化市自強路350號(自強路340巷)",
    "彰化縣彰化市泰和路二段145巷1號(泰和路-特教)",
    "彰化縣彰化市平和七街53號(真耶穌教會彰興教會)",
    "彰水路一段191巷",
    "彰化縣秀水鄉中山路290號(秀水縣立圖書館+鄉公所)",
    "彰化縣秀水鄉仁愛路22號(仁愛路)",
    "彰化縣秀水鄉中山路364號(中山路)",
    "秀水國小",
    "秀水明正國小",
    "和美地政",
    "彰化縣和美鎮仁愛路279號(和美仁愛路)",
    "彰化縣和美鎮鹿和路六段341號(和美鎮公所)",
    "和美圖書館",
    "彰化縣和美鎮和平街19號(和平街-和光路)",
    "彰化縣和美鎮彰美路六段236號(彰美路六段)",
    "和美國中(西園路)",
    "彰化縣和美鎮彰美路五段181號(和美念佛會)",
    "彰化縣彰化市西勢街210號(西勢街)",
    "彰化縣和美鎮嘉佃路290號(大榮)",
    "彰化大嘉國小",
    "彰化和東國小",
    "伸港圖書館",
    "彰化縣員林市惠明街319號(員林地方稅務)",
    "員林高中",
    "員林崇實",
    "員林市公所",
    "台北市中正區鎮江街5號(青島東路)",
    "台北市立成功高級中學(濟南路)",
    "台北市中正區戶政事務所(5號出口)",
    "警政署職場互助教保服務中心(北平東路)",
    "台北市稅捐稽徵處(北平東路)" 
]

class AddressApp:
    def __init__(self, root):
        self.root = root
        self.root.title("地址器")
        self.root.attributes('-topmost', True)
        self.checked_count = 0
        self.countdown_thread = None  # 用於追蹤倒數計時執行緒

        self.search_frame = tk.Frame(root)
        self.search_frame.pack(pady=10)
        self.search_entries = []
        for i in range(3):
            entry = tk.Entry(self.search_frame, width=10, font=("Microsoft JhengHei", 12))
            entry.pack(side="left", padx=5)
            self.search_entries.append(entry)

        self.btn_frame = tk.Frame(root)
        self.btn_frame.pack(pady=5)

        self.confirm_btn = tk.Button(self.btn_frame, text="確定", font=("Microsoft JhengHei", 12), command=self.search_and_check)
        self.confirm_btn.pack(side="left", padx=10)

        self.highlight_btn = tk.Button(self.btn_frame, text="搜尋", font=("Microsoft JhengHei", 12), command=self.highlight_matches)
        self.highlight_btn.pack(side="left", padx=10)

        self.clear_btn = tk.Button(self.btn_frame, text="清除", font=("Microsoft JhengHei", 12), command=self.clear_selection)
        self.clear_btn.pack(side="left", padx=10)

        self.count_label = tk.Label(self.btn_frame, text="", font=("Microsoft JhengHei", 12, "bold"), bg="black", fg="white", width=5)
        self.count_label.pack(side="left", padx=10)

        # 新增倒數計時標籤，固定寬度和背景色
        self.countdown_label = tk.Label(self.btn_frame, text="", font=("Microsoft JhengHei", 12, "bold"), 
                                      fg="white", bg="red", width=5, anchor="center")
        self.countdown_label.pack(side="left", padx=10)

        self.list_frame = tk.Frame(root)
        self.list_frame.pack(fill="both", expand=True, padx=10, pady=10)

        self.canvas = tk.Canvas(self.list_frame, height=800, width=600)
        self.scrollbar = tk.Scrollbar(self.list_frame, orient="vertical", command=self.canvas.yview)
        self.scrollable_frame = tk.Frame(self.canvas)

        self.scrollable_frame.bind(
            "<Configure>",
            lambda e: self.canvas.configure(scrollregion=self.canvas.bbox("all"))
        )

        self.canvas.create_window((0, 0), window=self.scrollable_frame, anchor="nw")
        self.canvas.configure(yscrollcommand=self.scrollbar.set)

        self.canvas.pack(side="left", fill="both", expand=True)
        self.scrollbar.pack(side="right", fill="y")

        self.canvas.bind_all("<MouseWheel>", self._on_mousewheel)
        self.canvas.bind_all("<Button-4>", self._on_mousewheel)
        self.canvas.bind_all("<Button-5>", self._on_mousewheel)

        self.checkbox_vars = []
        self.checkboxes = []
        self.labels = []

        for addr in addresses:
            self.add_address(addr)

        self.root.bind('<Return>', lambda e: self.search_and_check())
        self.root.bind('<space>', lambda e: self.highlight_matches())

    def _on_mousewheel(self, event):
        if event.num == 4:
            self.canvas.yview_scroll(-1, "units")
        elif event.num == 5:
            self.canvas.yview_scroll(1, "units")
        else:
            self.canvas.yview_scroll(int(-1 * (event.delta / 120)), "units")

    def add_address(self, address):
        var = tk.BooleanVar()
        container = tk.Frame(self.scrollable_frame)
        container.pack(anchor="w", pady=2, fill="x")

        cb = tk.Checkbutton(container, variable=var)
        cb.pack(side="left")

        main_addr = address.split("(")[0]
        bracket = address.split("(")[1][:-1] if "(" in address else ""

        label = tk.Label(container, text=main_addr, fg="#007bff", cursor="hand2", font=("Microsoft JhengHei", 12))
        label.pack(side="left", padx=(5, 5))

        bracket_label = tk.Label(container, text=f"({bracket})", font=("Microsoft JhengHei", 12, "italic"), fg="#555")
        bracket_label.pack(side="left")

        def on_click(event):
            current_index = len(self.checkbox_vars)  # 當前要加入的索引
            threading.Thread(target=self.paste_address_and_next, args=(main_addr, var, current_index)).start()

        label.bind("<Button-1>", on_click)

        def on_check(*args):
            self.update_checked_count()

        var.trace_add("write", lambda *args: on_check())

        self.checkbox_vars.append(var)
        self.checkboxes.append(cb)
        self.labels.append(label)

    def update_checked_count(self):
        count = sum(var.get() for var in self.checkbox_vars)
        self.checked_count = count
        self.count_label.config(text=f"{self.checked_count}")

    def paste_address_and_next(self, text, checkbox_var, current_index):
        # 取消勾選當前地址
        if checkbox_var.get():
            checkbox_var.set(False)

        # 貼上地址
        pyperclip.copy(text)
        try:
            wins = gw.getWindowsWithTitle(TARGET_WINDOW_TITLE)
            if not wins:
                print(f"找不到標題為「{TARGET_WINDOW_TITLE}」的視窗")
                return
            win = wins[0]
            win.activate()
            time.sleep(0.1)
            pyautogui.hotkey('ctrl', 'v')
            # 自動按 Enter 鍵
            time.sleep(0.1)  # 稍微等待貼上完成
            pyautogui.press('enter')
        except Exception as e:
            print(f"貼上失敗：{e}")
            return

        # 尋找下一個有勾選的地址並跳到該位置
        self.jump_to_next_checked(current_index)

        # 開始倒數計時並顯示在統一位置
        self.start_countdown()

    def start_countdown(self):
        # 如果有正在執行的倒數計時，不重複啟動
        if self.countdown_thread and self.countdown_thread.is_alive():
            return
            
        def countdown():
            for sec in range(30, 0, -1):
                self.countdown_label.config(text=f"{sec}s", bg="red")
                time.sleep(1)
            self.countdown_label.config(text="", bg="red")
            # 播放系統提示音
            winsound.PlaySound("SystemExclamation", winsound.SND_ALIAS)

        self.countdown_thread = threading.Thread(target=countdown)
        self.countdown_thread.start()

    def jump_to_next_checked(self, current_index):
        # 從當前索引的下一個開始找
        for i in range(current_index + 1, len(self.checkbox_vars)):
            if self.checkbox_vars[i].get():
                self.scroll_to_address(i)
                return
        
        # 如果後面沒有找到，從頭開始找到當前索引之前
        for i in range(0, current_index):
            if self.checkbox_vars[i].get():
                self.scroll_to_address(i)
                return

    def scroll_to_address(self, index):
        widget = self.checkboxes[index].master
        self.canvas.update_idletasks()
        y = widget.winfo_y()
        total_height = self.scrollable_frame.winfo_height()
        if total_height > 0:
            self.canvas.yview_moveto(y / total_height)
        else:
            self.canvas.yview_moveto(0)

    def paste_address(self, text, checkbox_var):
        if checkbox_var.get():
            checkbox_var.set(False)
        pyperclip.copy(text)
        try:
            wins = gw.getWindowsWithTitle(TARGET_WINDOW_TITLE)
            if not wins:
                print(f"找不到標題為「{TARGET_WINDOW_TITLE}」的視窗")
                return
            win = wins[0]
            win.activate()
            time.sleep(0.1)
            pyautogui.hotkey('ctrl', 'v')
            # 自動按 Enter 鍵
            time.sleep(0.1)  # 稍微等待貼上完成
            pyautogui.press('enter')
        except Exception as e:
            print(f"貼上失敗：{e}")
            return

        # 啟動統一的倒數計時
        self.start_countdown()

    def search_and_check(self):
        inputs = [e.get().strip() for e in self.search_entries]
        first_checked_index = None
        
        for i, addr in enumerate(addresses):
            if any(s and s in addr for s in inputs):
                if not self.checkbox_vars[i].get():
                    self.checkbox_vars[i].set(True)
                if first_checked_index is None:
                    first_checked_index = i

        # 跳到第一個勾選的地址
        if first_checked_index is not None:
            self.scroll_to_address(first_checked_index)

        for e in self.search_entries:
            e.delete(0, tk.END)
        self.update_checked_count()

    def highlight_matches(self):
        inputs = [e.get().strip() for e in self.search_entries]
        first_match_index = None
        for i, label in enumerate(self.labels):
            if any(s and s in addresses[i] for s in inputs):
                label.config(fg="red")
                if first_match_index is None:
                    first_match_index = i
            else:
                label.config(fg="#007bff")

        if first_match_index is not None:
            widget = self.checkboxes[first_match_index].master
            self.canvas.update_idletasks()
            y = widget.winfo_y()
            total_height = self.scrollable_frame.winfo_height()
            if total_height > 0:
                self.canvas.yview_moveto(y / total_height)
            else:
                self.canvas.yview_moveto(0)

        # 清除輸入框內容
        for e in self.search_entries:
            e.delete(0, tk.END)

    def clear_selection(self):
        for var in self.checkbox_vars:
            var.set(False)
        self.update_checked_count()

if __name__ == "__main__":
    root = tk.Tk()
    app = AddressApp(root)
    root.mainloop()
<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <title>文字轉換工具</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
        }
        textarea, p {
            width: 100%;
            max-width: 600px;
            margin-bottom: 10px;
        }
        button {
            padding: 10px 20px;
            background-color: #4CAF50;
            color: white;
            border: none;
            cursor: pointer;
        }
        button:hover {
            background-color: #45a049;
        }
        #outputText {
            white-space: pre-wrap;
            background-color: #f5f5f5;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
    </style>
</head>
<body>
    <h2>文字轉換工具</h2>

    <div style="margin-bottom: 15px; padding: 10px; background-color: #f9f9f9; border-radius: 4px;">
        <div style="margin-bottom: 10px;">
            <label>
                <input type="radio" name="rangeMode" value="range" checked>
                數字範圍模式
            </label>
            <input type="text" id="numberRange" value="1-100" placeholder="例如：1-100" style="margin-left: 10px; padding: 5px;">
            <br><small style="color: #666;">只有在此範圍內的數字開頭才會被視為新段落</small>
        </div>
        <div>
            <label>
                <input type="radio" name="rangeMode" value="sequence">
                連續數字模式
            </label>
            <input type="text" id="sequenceRange" value="70-80" placeholder="例如：70-80" style="margin-left: 10px; padding: 5px;">
            <br><small style="color: #666;">檢查連續數字對（如72,73或1,2,3）- 只要有相鄰的連續數字就會分段</small>
        </div>
    </div>

    <p>請輸入要轉換的文字（包含換行）：</p>
    <textarea id="inputText" rows="12" placeholder="輸入文字...">72因我國刑事案件是由「被告」與「犯罪事實」構成，數被告共同為一個犯罪事
實時，會被評價為數案件。因此，雖然修法後論及文化抗辯的案件數為39個，
但因存在數被告共同獵捕野生動物的共犯案件，故實際上僅有25個獵捕野生動
物的犯罪事實。考慮法官評價數被告所為的一個獵捕犯罪事實時，作為評價基礎的獵捕動物種類、數量與獵捕目的是相同的，對該事實的評價並不會因不同
被告而有異，故宜以25個犯罪事實為迴歸分析之樣本。
732001年三讀通過立法理由為：「原住民使用獵槍是有其生活上之需要，以法律
制裁持有生活必需品之行為，是對原住民人權之嚴重傷害。因此，原住民持有
獵槍者只要登記即可合法，而未經登記者則以行政罰加以處罰，這不但符合行
政程序法之規定，也保障了原住民基本之生活權益」。
999這個不會分段，因為沒有相鄰的連續數字
其他內容...</textarea>
    <button onclick="convertText()">轉換文字</button>
    <div style="display: flex; align-items: center; gap: 10px; margin-top: 20px;">
        <h3 style="margin: 0;">轉換結果：</h3>
        <button id="copyButton" onclick="copyResult()" style="display: none; padding: 5px 10px; font-size: 12px;">複製結果</button>
    </div>
    <p id="outputText"></p>

    <script>
        function isNumberInRange(number, rangeStr) {
            if (!rangeStr || rangeStr.trim() === '') return true;

            const parts = rangeStr.split('-');
            if (parts.length !== 2) return true;

            const min = parseInt(parts[0].trim());
            const max = parseInt(parts[1].trim());

            if (isNaN(min) || isNaN(max)) return true;

            return number >= min && number <= max;
        }

        function extractParagraphNumber(line) {
            // 智能段落編號提取
            // 特別處理 "732001" 這種情況，應該提取 "73"

            // 首先檢查是否是 "數字+年份" 的模式
            const yearPattern = /^(\d{1,3})(\d{4})/;
            const yearMatch = line.match(yearPattern);

            if (yearMatch) {
                const possibleParagraph = parseInt(yearMatch[1]);
                const yearPart = yearMatch[2];

                // 如果年份部分看起來像年份（19xx, 20xx, 21xx）
                if (yearPart.startsWith('19') || yearPart.startsWith('20') || yearPart.startsWith('21')) {
                    return possibleParagraph;
                }
            }

            // 一般情況：數字後面跟非數字字符
            const generalPattern = /^(\d{1,3})([^\d]|$)/;
            const generalMatch = line.match(generalPattern);

            if (generalMatch) {
                const number = parseInt(generalMatch[1]);
                const afterNumber = generalMatch[2] || '';

                // 確保不是純數字（避免誤判年份等）
                if (afterNumber !== '' || line.length <= 3) {
                    return number;
                }
            }

            return null;
        }

        function validateSequentialNumbers(lines) {
            // 提取所有段落編號
            const numbers = [];

            for (let line of lines) {
                line = line.trim();
                if (line === '') continue;

                const number = extractParagraphNumber(line);
                if (number !== null) {
                    numbers.push(number);
                }
            }

            // 檢查連續序列範圍
            const sequenceRange = document.getElementById("sequenceRange").value;
            const parts = sequenceRange.split('-');
            if (parts.length !== 2) return new Set();

            const rangeStart = parseInt(parts[0].trim());
            const rangeEnd = parseInt(parts[1].trim());

            if (isNaN(rangeStart) || isNaN(rangeEnd)) return new Set();

            // 找出在範圍內的數字並排序
            const numbersInRange = numbers.filter(n => n >= rangeStart && n <= rangeEnd).sort((a, b) => a - b);

            // 去除重複數字
            const uniqueNumbers = [...new Set(numbersInRange)];

            if (uniqueNumbers.length === 0) return new Set();

            // 檢查連續性 - 更寬鬆的邏輯
            const validNumbers = new Set();

            // 如果只有一個數字，且在範圍內，就認為有效
            if (uniqueNumbers.length === 1) {
                validNumbers.add(uniqueNumbers[0]);
                return validNumbers;
            }

            // 檢查是否有連續的數字對（相鄰數字）
            for (let i = 0; i < uniqueNumbers.length - 1; i++) {
                const current = uniqueNumbers[i];
                const next = uniqueNumbers[i + 1];

                // 如果當前數字和下一個數字是連續的
                if (next === current + 1) {
                    validNumbers.add(current);
                    validNumbers.add(next);
                }
            }

            return validNumbers;
        }

        function shouldStartNewParagraph(line, validSequenceNumbers = null) {
            const rangeMode = document.querySelector('input[name="rangeMode"]:checked').value;

            if (rangeMode === 'sequence') {
                // 連續數字模式：只有在有效序列中的數字才能分段
                const number = extractParagraphNumber(line);
                return number !== null && validSequenceNumbers && validSequenceNumbers.has(number);
            } else {
                // 範圍模式：在範圍內的數字都可以分段
                const number = extractParagraphNumber(line);
                if (number === null) return false;

                const rangeStr = document.getElementById("numberRange").value;
                return isNumberInRange(number, rangeStr);
            }
        }

        function convertText() {
            // 取得輸入文字
            let input = document.getElementById("inputText").value;

            // 先按行分割，但保留每行原本的空格
            let lines = input.split('\n');
            let result = [];
            let currentParagraph = '';

            // 如果使用連續數字模式，先驗證序列
            let validSequenceNumbers = null;
            const rangeMode = document.querySelector('input[name="rangeMode"]:checked').value;
            if (rangeMode === 'sequence') {
                validSequenceNumbers = validateSequentialNumbers(lines);

                // 調試信息：顯示提取到的數字
                console.log('=== 調試信息 ===');
                for (let line of lines) {
                    line = line.trim();
                    if (line === '') continue;
                    const extractedNumber = extractParagraphNumber(line);
                    if (extractedNumber !== null) {
                        console.log(`行: "${line.substring(0, 20)}..." → 提取數字: ${extractedNumber}`);
                    }
                }

                // 如果沒有找到有效的連續序列，顯示提示
                if (validSequenceNumbers.size === 0) {
                    const sequenceRange = document.getElementById("sequenceRange").value;
                    console.log(`提示：未找到連續數字序列 (${sequenceRange})。`);
                } else {
                    const validArray = Array.from(validSequenceNumbers).sort((a, b) => a - b);
                    console.log(`找到連續序列：${validArray.join(', ')}`);
                }
            }

            for (let line of lines) {
                // 只移除行尾的換行符號，保留其他空格
                if (line.trim() === '') continue; // 跳過空行

                // 檢查是否應該開始新段落（使用trim後的版本來檢查）
                if (shouldStartNewParagraph(line.trim(), validSequenceNumbers)) {
                    // 如果當前段落不為空，先加入結果
                    if (currentParagraph) {
                        result.push(currentParagraph.trim());
                    }
                    // 開始新段落，保留原本的空格
                    currentParagraph = line;
                } else {
                    // 繼續當前段落，直接連接保留原本空格
                    currentParagraph += line;
                }
            }

            // 加入最後一個段落
            if (currentParagraph) {
                result.push(currentParagraph.trim());
            }

            // 用換行符號連接各段落
            let output = result.join('\n');

            // 顯示結果
            document.getElementById("outputText").innerText = output;

            // 顯示複製按鈕
            const copyButton = document.getElementById("copyButton");
            if (output.trim()) {
                copyButton.style.display = "inline-block";
            } else {
                copyButton.style.display = "none";
            }
        }

        function copyResult() {
            const outputText = document.getElementById("outputText").innerText;

            // 使用現代的 Clipboard API
            if (navigator.clipboard && window.isSecureContext) {
                navigator.clipboard.writeText(outputText).then(() => {
                    showCopyFeedback();
                }).catch(err => {
                    console.error('複製失敗:', err);
                    fallbackCopy(outputText);
                });
            } else {
                // 降級方案
                fallbackCopy(outputText);
            }
        }

        function fallbackCopy(text) {
            const textArea = document.createElement("textarea");
            textArea.value = text;
            textArea.style.position = "fixed";
            textArea.style.left = "-999999px";
            textArea.style.top = "-999999px";
            document.body.appendChild(textArea);
            textArea.focus();
            textArea.select();

            try {
                document.execCommand('copy');
                showCopyFeedback();
            } catch (err) {
                console.error('複製失敗:', err);
                alert('複製失敗，請手動選取文字複製');
            }

            document.body.removeChild(textArea);
        }

        function showCopyFeedback() {
            const copyButton = document.getElementById("copyButton");
            const originalText = copyButton.innerText;
            copyButton.innerText = "已複製！";
            copyButton.style.backgroundColor = "#28a745";

            setTimeout(() => {
                copyButton.innerText = originalText;
                copyButton.style.backgroundColor = "#4CAF50";
            }, 2000);
        }
    </script>
</body>
</html>
